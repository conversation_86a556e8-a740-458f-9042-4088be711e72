import { get } from '@/libs/utils/api';
import type { OrderHistoryItemType } from '@/libs/orders/types';
import type { GetDataWithPagination } from '@/types/utility';
import { OrderListSearchParamsType } from './useOrderListSearchParams';
import { useQuery } from '@tanstack/react-query';

interface fetchOrderListProps {
  searchParams: OrderListSearchParamsType;
  beforeStart?: VoidFunction;
  onSuccess?: (data: { orders: OrderHistoryItemType[]; total: number }) => void;
  onError?: VoidFunction;
  afterDone?: VoidFunction;
}

// TODO: Make it generic and re-useable
const parseQueryParams = (searchParams: OrderListSearchParamsType) => {
  const {
    query,
    dateFromFilter,
    dateToFilter,
    page,
    perPage,
    // TODO: Add sortd
    // sort,
  } = searchParams;

  let queryString = '';
  queryString += query ? `filter[search]=${query}` : '';
  queryString += dateFromFilter ? `&filter[date_from]=${dateFromFilter}` : '';
  queryString += dateToFilter ? `&filter[date_to]=${dateToFilter}` : '';
  queryString += page ? `&page[number]=${page}` : '';
  queryString += perPage ? `&page[size]=${perPage}` : '';

  return queryString;
};

export const fetchOrderList = async ({
  searchParams,
  beforeStart = () => {},
  onSuccess = () => {},
  onError = () => {},
  afterDone = () => {},
}: fetchOrderListProps) => {
  beforeStart();

  const queryString = parseQueryParams(searchParams);
  try {
    const { data = [], isLoading } = useQuery({
      queryKey,
      queryFn: async () => {
        try {
          const response = await get<{ data: string[] }>({
            url: `/clinics/${clinic?.id}/autocomplete?query=${encodeURIComponent(debouncedQuery)}&clinicId=${clinic?.id}`,
          });

          const results = response?.data || [];

          if (results.length === 0) {
            setLastEmptyQuery(debouncedQuery);
          } else {
            setLastEmptyQuery('');
          }

          return results;
        } catch (error) {
          setLastEmptyQuery(debouncedQuery);
          throw error;
        }
      },
      enabled: !shouldSkipQuery(),
      staleTime: 5 * 60 * 1000,
    });

    const response = await get<GetDataWithPagination<OrderHistoryItemType>>({
      url: `/orders/?${queryString}`,
    });

    onSuccess({
      orders: response.data,
      total: response.meta.total,
    });
  } catch {
    onError();
  }

  afterDone();
};
